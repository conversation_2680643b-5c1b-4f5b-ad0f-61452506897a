/**
 * Модуль AJAX запросов для формы создания платежа работнику
 * Содержит функции для взаимодействия с сервером
 */

// Глобальный объект для AJAX функций
window.WorkerPaymentAjax = {

    /**
     * Загрузка информации о работнике
     */
    loadWorkerInfo: function (workerId, month) {
        $.ajax({
            url: '/backend/worker-payment/get-worker-info',
            type: 'GET',
            data: {
                worker_id: workerId,
                month: month
            },
            success: function (response) {
                if (response.success) {
                    $('#worker-salary').text(window.WorkerPaymentCalculations.formatNumber(response.salary));

                    // Вычисляем общую сумму выплат только по зарплате и авансу (исключаем долги и другие выплаты)
                    var salaryPaid = response.payments[WORKER_FINANCES_TYPE_SALARY] || 0;
                    var advancePaid = response.payments[WORKER_FINANCES_TYPE_ADVANCE] || 0;
                    var totalSalaryAndAdvancePaid = salaryPaid + advancePaid;

                    $('#worker-total-paid').text(window.WorkerPaymentCalculations.formatNumber(totalSalaryAndAdvancePaid));
                    $('#worker-info').show();

                    // Show/hide debt section based on debt amount
                    if (response.debt && response.debt > 0) {
                        $('#worker-debt-amount').text(window.WorkerPaymentCalculations.formatNumber(response.debt));
                        $('#debt-payment-section').show();

                        // Show remaining salary info (будет пересчитано позже)
                        $('#remaining-salary-info').show();
                    } else {
                        $('#debt-payment-section').hide();
                        $('#debt-payment-checkbox').prop('checked', false).trigger('change');

                        // Показываем оставшуюся зарплату, если есть что показать
                        var remainingSalary = response.salary - totalSalaryAndAdvancePaid;

                        if (remainingSalary > 0) {
                            $('#worker-remaining-salary').text(window.WorkerPaymentCalculations.formatNumber(remainingSalary));
                            $('#remaining-salary-info').show();
                        } else {
                            $('#remaining-salary-info').hide();
                        }
                    }

                    // Update payment amounts based on existing payments
                    window.WorkerPaymentCalculations.updatePaymentAmounts(response.payments, response.salary);
                } else {
                    alert(response.message);
                }
            },
            error: function () {
                alert('Ошибка при загрузке информации о работнике');
            }
        });
    },

    /**
     * Подготовка данных формы для отправки
     */
    prepareFormData: function () {
        var data = {
            worker_id: $('#worker_id').val(),
            month: $('#month').val(),
            payment_types: {}
        };

        // Собираем данные по типам платежей
        $('.payment-type-checkbox:checked').each(function () {
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            var selectedMethods = row.find('.payment-method-checkbox:checked');
            var dynamicAmounts = row.find('.dynamic-amounts input');

            if (selectedMethods.length === 1) {
                // Один метод оплаты
                var amount = window.WorkerPaymentCalculations.getNumericValue(row.find('.amount-input').val());
                if (amount > 0) {
                    data.payment_types[typeId] = {
                        amounts: {}
                    };
                    data.payment_types[typeId].amounts[selectedMethods.val()] = amount;
                }
            } else if (selectedMethods.length > 1 && dynamicAmounts.length > 0) {
                // Несколько методов оплаты
                data.payment_types[typeId] = {
                    amounts: {}
                };

                dynamicAmounts.each(function () {
                    var amount = window.WorkerPaymentCalculations.getNumericValue($(this).val());
                    var methodType = $(this).attr('name').match(/\[(\d+)\]$/)[1];
                    if (amount > 0) {
                        data.payment_types[typeId].amounts[methodType] = amount;
                    }
                });
            }
        });

        // Добавляем данные о погашении долга
        if ($('#debt-payment-checkbox').is(':checked')) {
            var debtAmount = window.WorkerPaymentCalculations.getNumericValue($('#debt-payment-amount').val());
            if (debtAmount > 0) {
                data.payment_types[WORKER_FINANCES_TYPE_DEBT_PAYMENT] = {
                    amounts: {}
                };
                data.payment_types[WORKER_FINANCES_TYPE_DEBT_PAYMENT].amounts[PAYMENT_TYPE_CASH] = debtAmount; // Долг всегда погашается наличными
            }
        }

        return data;
    },

    /**
     * Отправка данных на сервер
     */
    submitPayment: function (data) {
        var submitBtn = $('.worker-payment-submit-button'); // Используем класс вместо ID
        var originalText = submitBtn.html();

        // Блокируем кнопку и показываем загрузку
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Обработка...');

        $.ajax({
            url: '/backend/worker-payment/store',
            type: 'POST',
            data: data,
            dataType: 'json',
            success: function (response) {
                console.log('Server response:', response);

                if (response.success || response.status === 'success') {
                    // Успешное создание платежа
                    if (typeof window.WorkerPaymentUI !== 'undefined') {
                        window.WorkerPaymentUI.showSuccessMessage('Платеж успешно создан!');
                    }

                    // Проверяем, находимся ли мы в модалке
                    if ($('.modal').length > 0 && $('.modal').hasClass('show')) {
                        // Если в модалке, вызываем событие для обновления списка
                        $(document).trigger('paymentCreated');
                    } else {
                        // Если не в модалке, перенаправляем на страницу списка
                        setTimeout(function () {
                            window.location.href = '/backend/worker-payment';
                        }, 2000);
                    }
                } else {
                    // Ошибка валидации или создания
                    var errorMessage = response.message || 'Ошибка при создании платежа';
                    console.error('Payment creation error:', errorMessage);

                    if (typeof window.WorkerPaymentUI !== 'undefined') {
                        window.WorkerPaymentUI.showErrorMessage(errorMessage);

                        // Показываем ошибки валидации
                        if (response.errors) {
                            window.WorkerPaymentUI.showServerValidationErrors(response.errors);
                        }
                    } else {
                        alert(errorMessage);
                    }
                }
            },
            error: function (xhr, status, error) {
                console.log('AJAX Error Details:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });

                var errorMessage = 'Ошибка сервера: ' + error;
                if (typeof window.WorkerPaymentUI !== 'undefined') {
                    window.WorkerPaymentUI.showErrorMessage(errorMessage);
                } else {
                    alert(errorMessage);
                }
            },
            complete: function () {
                // Разблокируем кнопку
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    }
};

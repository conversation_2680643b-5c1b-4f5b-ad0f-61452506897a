<?php

namespace app\modules\backend\controllers;

use app\modules\backend\models\Worker;
use app\modules\backend\models\WorkerFinances;
use app\modules\backend\models\WorkerDebt;
use app\modules\backend\models\WorkerSalary;
use Yii;
use yii\web\Response;

class WorkerPaymentController extends BaseController
{
    public function actionIndex()
    {
        $month = Yii::$app->request->get('month', date('Y-m'));
        
        $sql = "SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата за месяц)
            COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) as card_payment,
            
            -- Долг (непогашенные долги)
            COALESCE(wd.total_debt, 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as debt_deduction,
            
            -- Вычисляем получено на руки
            COALESCE(ws.amount, 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt
        ORDER BY w.full_name";
        
        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':month', $month);
        $command->bindValue(':bonus_type', WorkerFinances::TYPE_BONUS);
        $command->bindValue(':vacation_type', WorkerFinances::TYPE_VACATION_PAY);
        $command->bindValue(':advance_type', WorkerFinances::TYPE_ADVANCE);
        $command->bindValue(':salary_type', WorkerFinances::TYPE_SALARY);
        $command->bindValue(':debt_payment_type', WorkerFinances::TYPE_DEBT_PAYMENT);
        
        $result = $command->queryAll();
        
        return $this->render('index', [
            'result' => $result,
            'month' => $month
        ]);
    }
    
    public function actionSearch()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $month = Yii::$app->request->post('month', date('Y-m'));
        
        $sql = "SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата за месяц)
            COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) as card_payment,
            
            -- Долг (непогашенные долги)
            COALESCE(wd.total_debt, 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as debt_deduction,
            
            -- Вычисляем получено на руки
            COALESCE(ws.amount, 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt
        ORDER BY w.full_name";
        
        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':month', $month);
        $command->bindValue(':bonus_type', WorkerFinances::TYPE_BONUS);
        $command->bindValue(':vacation_type', WorkerFinances::TYPE_VACATION_PAY);
        $command->bindValue(':advance_type', WorkerFinances::TYPE_ADVANCE);
        $command->bindValue(':salary_type', WorkerFinances::TYPE_SALARY);
        $command->bindValue(':debt_payment_type', WorkerFinances::TYPE_DEBT_PAYMENT);
        
        $result = $command->queryAll();
        
        return [
            'status' => 'success',
            'data' => $result
        ];
    }
    
    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $service = new \app\modules\backend\services\worker_payment\WorkerPaymentService();
        $formData = $service->getFormData();
        
        return [
            "status" => true,
            "content" => $this->renderPartial('create', $formData)
        ];
    }
    
    public function actionStore()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $service = new \app\modules\backend\services\worker_payment\WorkerPaymentService();
        $result = $service->createPayment(Yii::$app->request->post());
        
        return $result;
    }
    
    public function actionGetWorkerInfo()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $workerId = Yii::$app->request->get('worker_id');
        $month = Yii::$app->request->get('month', date('Y-m'));
        
        if (!$workerId) {
            return [
                'status' => 'error',
                'message' => 'Не указан ID работника'
            ];
        }

        $service = new \app\modules\backend\services\worker_payment\WorkerPaymentService();
        $workerInfo = $service->getWorkerSalaryInfo($workerId);
        $paymentsInfo = $service->getWorkerPaymentsForMonth($workerId, $month);

        if ($workerInfo['status'] === 'error') {
            return $workerInfo;
        }

        // Получаем информацию о долге
        $debt = WorkerDebt::find()
            ->where(['worker_id' => $workerId])
            ->andWhere(['>', 'amount', 0])
            ->andWhere(['deleted_at' => null])
            ->sum('amount') ?? 0;

        return [
            'status' => 'success',
            'worker' => $workerInfo['worker'],
            'salary' => $workerInfo['salary'],
            'has_salary' => $workerInfo['has_salary'],
            'payments' => $paymentsInfo['payments_by_type'] ?? [],
            'total_paid' => $paymentsInfo['total_paid'] ?? 0,
            'debt' => $debt
        ];
    }
    
    public function actionExport()
    {
        $month = Yii::$app->request->get('month', date('Y-m'));
        
        $sql = "SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата за месяц)
            COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) as card_payment,
            
            -- Долг (непогашенные долги)
            COALESCE(wd.total_debt, 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as debt_deduction,
            
            -- Вычисляем получено на руки
            COALESCE(ws.amount, 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt
        ORDER BY w.full_name";
        
        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':month', $month);
        $command->bindValue(':bonus_type', WorkerFinances::TYPE_BONUS);
        $command->bindValue(':vacation_type', WorkerFinances::TYPE_VACATION_PAY);
        $command->bindValue(':advance_type', WorkerFinances::TYPE_ADVANCE);
        $command->bindValue(':salary_type', WorkerFinances::TYPE_SALARY);
        $command->bindValue(':debt_payment_type', WorkerFinances::TYPE_DEBT_PAYMENT);
        
        $result = $command->queryAll();
        
        // Подготавливаем данные для Excel
        $data = [];
        $data[] = [
            'ФИО',
            'Должность',
            'Оклад',
            'Бонус',
            'Отпускные',
            'Аванс',
            'Оплата по карте',
            'Долг',
            'Удержания долга',
            'Получено на руки'
        ];
        
        foreach ($result as $row) {
            $data[] = [
                $row['full_name'],
                $row['position'],
                $row['salary'] ?? 0,
                $row['bonus'] ?? 0,
                $row['vacation_pay'] ?? 0,
                $row['advance'] ?? 0,
                $row['card_payment'] ?? 0,
                $row['debt'] ?? 0,
                $row['debt_deduction'] ?? 0,
                $row['received_amount'] ?? 0
            ];
        }
        
        // Создаем CSV файл
        $filename = 'worker_payment_report_' . $month . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Добавляем BOM для корректного отображения в Excel
        fwrite($output, "\xEF\xBB\xBF");
        
        foreach ($data as $row) {
            fputcsv($output, $row, ';');
        }
        
        fclose($output);
        exit;
    }
}
